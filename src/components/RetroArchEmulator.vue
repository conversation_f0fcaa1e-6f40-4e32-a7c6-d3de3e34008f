<template>
  <div :class="['w-full', isFullscreen ? 'mobile-fullscreen' : 'space-y-6']">
    <!-- Game Container -->
    <div
      :class="[
        'relative bg-black rounded-xl overflow-hidden shadow-2xl',
        isFullscreen ? 'w-full h-full' : 'game-viewport mx-auto',
      ]"
    >
      <!-- Loading Overlay -->
      <div
        v-if="loading"
        class="absolute inset-0 z-10 flex items-center justify-center bg-black/80 backdrop-blur-sm"
      >
        <div class="space-y-4 text-center">
          <div class="relative">
            <div class="mx-auto loading-spinner"></div>
            <div class="pulse-ring"></div>
            <div class="pulse-ring"></div>
            <div class="pulse-ring"></div>
          </div>
          <h3 class="text-xl font-semibold text-white">
            正在加载RetroArch模拟器...
          </h3>
          <p class="text-gray-300">正在下载 {{ core?.name }} 核心...</p>
          <div class="w-64 h-2 mx-auto bg-gray-700 rounded-full">
            <div
              class="h-2 rounded-full bg-primary-500 animate-pulse"
              style="width: 45%"
            ></div>
          </div>
        </div>
      </div>

      <!-- Game Canvas -->
      <div
        id="canvas"
        class="flex items-center justify-center w-full h-full bg-black min-h-[400px]"
        style="width: 100%; height: 100%; min-height: 400px"
      ></div>
    </div>

    <!-- Controls -->
    <div
      :class="[
        'game-controls',
        isFullscreen ? 'fixed bottom-0 left-0 right-0 z-30 rounded-none' : '',
      ]"
    >
      <!-- <button
        v-if="!loading && !hasProgress"
        @click="startGame"
        class="flex items-center gap-2 btn-primary"
      >
        <span class="text-lg">🎮</span>
        开始游戏
      </button>

      <button
        v-if="!loading && hasProgress"
        @click="startGameWithProgress"
        class="flex items-center gap-2 btn-primary"
      >
        <span class="text-lg">▶️</span>
        继续游戏
      </button> -->

      <button
        @click="stopEmulator"
        class="flex items-center gap-2 btn-secondary"
      >
        <span class="text-lg">⏹️</span>
        停止游戏
      </button>

      <!-- 保存相关按钮 -->
      <button
        v-if="!loading"
        @click="manualSave"
        :disabled="isAutoSaving"
        class="flex items-center gap-2 btn-secondary"
        title="手动保存游戏进度"
      >
        <span class="text-lg">💾</span>
        {{ isAutoSaving ? "保存中..." : "保存" }}
      </button>

      <button
        v-if="!loading"
        @click="quickSave"
        class="flex items-center gap-2 btn-secondary"
        title="快速保存"
      >
        <span class="text-lg">⚡</span>
        快存
      </button>

      <button
        v-if="!loading"
        @click="showSaveMenu = !showSaveMenu"
        class="flex items-center gap-2 btn-secondary"
        title="存档管理"
      >
        <span class="text-lg">📂</span>
        存档
      </button>

      <button
        @click="$emit('back')"
        class="flex items-center gap-2 btn-secondary"
      >
        <span class="text-lg">←</span>
        返回选择
      </button>

      <!-- Status Indicator -->
      <div class="flex items-center gap-2">
        <div
          :class="[
            'status-indicator',
            loading ? 'status-loading' : 'status-playing',
          ]"
        >
          <div class="w-2 h-2 mr-2 bg-current rounded-full animate-pulse"></div>
          {{ loading ? "加载中" : "运行中" }}
        </div>
      </div>
    </div>

    <!-- 存档管理面板 -->
    <div
      v-if="showSaveMenu"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      @click.self="showSaveMenu = false"
    >
      <div
        class="w-full max-w-md p-6 mx-4 overflow-y-auto bg-white rounded-lg max-h-96"
      >
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-bold">存档管理</h3>
          <button
            @click="showSaveMenu = false"
            class="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        <!-- 自动保存状态 -->
        <div v-if="lastSaveTime" class="p-3 mb-4 rounded-lg bg-green-50">
          <div class="text-sm text-green-700">
            <span class="font-medium">自动保存:</span>
            {{ lastSaveTime.toLocaleString() }}
          </div>
        </div>

        <!-- 存档列表 -->
        <div v-if="saveStates.length > 0" class="mb-4 space-y-2">
          <h4 class="font-medium text-gray-700">存档列表</h4>
          <div
            v-for="saveState in saveStates"
            :key="saveState.id"
            class="flex items-center justify-between p-3 border rounded-lg"
            :class="{
              'bg-blue-50 border-blue-200': saveState.autoSave,
              'bg-yellow-50 border-yellow-200': saveState.id.includes('_quick'),
              'bg-gray-50':
                !saveState.autoSave && !saveState.id.includes('_quick'),
            }"
          >
            <div class="flex-1">
              <div class="text-[#333] font-medium flex items-center gap-2">
                <span v-if="saveState.autoSave" class="text-blue-600">🔄</span>
                <span
                  v-else-if="saveState.id.includes('_quick')"
                  class="text-yellow-600"
                  >⚡</span
                >
                <span v-else class="text-gray-600">💾</span>
                {{ saveState.description }}
              </div>
              <div class="text-sm text-gray-500">
                {{ new Date(saveState.saveTime).toLocaleString() }}
              </div>
            </div>
            <div class="flex gap-2">
              <button
                @click="loadSaveState(saveState.id)"
                class="px-3 py-1 text-sm text-white bg-blue-500 rounded hover:bg-blue-600"
              >
                读取
              </button>
              <button
                @click="exportSaveState(saveState.id)"
                class="px-3 py-1 text-sm text-white bg-green-500 rounded hover:bg-green-600"
              >
                导出
              </button>
              <button
                @click="
                  deleteSaveState(
                    saveState.id,
                    saveState.description || '未命名存档'
                  )
                "
                class="px-3 py-1 text-sm text-white bg-red-500 rounded hover:bg-red-600"
                :disabled="saveState.autoSave"
                :class="{ 'opacity-50 cursor-not-allowed': saveState.autoSave }"
                :title="saveState.autoSave ? '自动保存无法删除' : '删除此存档'"
              >
                删除
              </button>
            </div>
          </div>
        </div>

        <!-- 导入存档 -->
        <div class="pt-4 border-t">
          <label class="block mb-2 text-sm font-medium text-gray-700">
            导入存档文件
          </label>
          <input
            type="file"
            accept=".json"
            @change="handleFileImport"
            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from "vue";
import type { GameFile, EmulatorCore } from "../types/emulator";
import toast from "../utils/toast";
import {
  getBiosFile,
  getBiosFileUrl,
  storeBiosFile,
} from "../utils/biosStorage";
import {
  getRecommendedPresetBios,
  getRecommendedPresetBiosByCore,
  downloadAndInstallPresetBios,
  type PresetBiosItem,
} from "../utils/presetBiosManager";
import { saveStateManager, type SaveState } from "../utils/saveStateManager";

const props = defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
  loading: boolean;
}>();

const emit = defineEmits<{
  error: [message: string];
  stop: [];
  back: [];
}>();

const loading = ref(true);
const isFullscreen = ref(false);
const isPortrait = ref(window.innerHeight > window.innerWidth);

// 进度保存相关
const hasProgress = ref(false);
const showSaveMenu = ref(false);
const saveStates = ref<SaveState[]>([]);
const isAutoSaving = ref(false);
const lastSaveTime = ref<Date | null>(null);

// 进度保存相关方法
async function checkGameProgress(): Promise<void> {
  try {
    await saveStateManager.init();
    hasProgress.value = await saveStateManager.hasGameProgress(props.game.name);
    console.log(
      `🎮 游戏 ${props.game.name} 进度检查:`,
      hasProgress.value ? "有存档" : "无存档"
    );
  } catch (error) {
    console.error("检查游戏进度失败:", error);
  }
}

async function startGameWithProgress(): Promise<void> {
  try {
    // 优先获取自动存档，然后是快速存档，最后是手动存档
    const progress = await saveStateManager.getGameProgress(props.game.name);
    let targetSave = null;

    if (progress?.autoSave) {
      targetSave = progress.autoSave;
      console.log("🎮 继续游戏，将加载自动存档...");
    } else if (progress?.quickSave) {
      targetSave = progress.quickSave;
      console.log("🎮 继续游戏，将加载快速存档...");
    } else {
      const latestSave = await saveStateManager.getLatestSaveState(
        props.game.name
      );
      if (latestSave) {
        targetSave = latestSave;
        console.log("🎮 继续游戏，将加载最新手动存档...");
      }
    }

    await startGame();

    // 等待游戏启动后再加载存档
    if (targetSave) {
      setTimeout(async () => {
        try {
          await saveStateManager.loadSaveState(targetSave.id);
          console.log("✅ 存档加载成功");
        } catch (error) {
          console.error("加载存档失败:", error);
        }
      }, 2000);
    }
  } catch (error) {
    console.error("启动游戏失败:", error);
    await startGame(); // 回退到普通启动
  }
}

async function manualSave(): Promise<void> {
  try {
    isAutoSaving.value = true;

    // 传递游戏名称作为备用参数
    const saveState = await saveStateManager.manualSaveGame(
      undefined,
      props.game.name
    );
    if (saveState) {
      lastSaveTime.value = saveState.saveTime;
      await loadSaveStates();
      console.log("💾 手动保存成功");
    }
  } catch (error) {
    console.error("手动保存失败:", error);
  } finally {
    isAutoSaving.value = false;
  }
}

async function quickSave(): Promise<void> {
  try {
    const saveState = await saveStateManager.quickSaveGame(props.game.name);
    if (saveState) {
      lastSaveTime.value = saveState.saveTime;
      console.log("⚡ 快速保存成功");
    }
  } catch (error) {
    console.error("快速保存失败:", error);
  }
}

async function loadSaveStates(): Promise<void> {
  try {
    const progress = await saveStateManager.getGameProgress(props.game.name);
    if (progress) {
      // 包含手动保存和自动保存
      const allSaves = [...progress.saveStates];

      // 如果有自动保存，添加到列表开头
      if (progress.autoSave) {
        allSaves.unshift(progress.autoSave);
      }

      // 如果有快速保存，添加到列表开头
      if (progress.quickSave) {
        allSaves.unshift(progress.quickSave);
      }

      saveStates.value = allSaves;
    }
  } catch (error) {
    console.error("加载存档列表失败:", error);
  }
}

async function loadSaveState(saveStateId: string): Promise<void> {
  try {
    await saveStateManager.loadSaveState(saveStateId);
    console.log("📂 读档成功");
  } catch (error) {
    console.error("读档失败:", error);
  }
}

async function deleteSaveState(
  saveStateId: string,
  description: string
): Promise<void> {
  try {
    // 确认删除
    const confirmed = confirm(
      `确定要删除存档"${description}"吗？此操作无法撤销。`
    );
    if (!confirmed) return;

    // 删除存档
    await saveStateManager.deleteSaveState(saveStateId);

    // 如果删除的是快速保存或自动保存，需要从游戏进度中移除
    const progress = await saveStateManager.getGameProgress(props.game.name);
    if (progress) {
      if (progress.quickSave?.id === saveStateId) {
        await saveStateManager.removeQuickSave(props.game.name);
      }
      if (progress.autoSave?.id === saveStateId) {
        await saveStateManager.removeAutoSave(props.game.name);
      }

      // 如果是手动保存，从手动保存列表中移除
      await saveStateManager.removeManualSave(props.game.name, saveStateId);
    }

    // 重新加载存档列表
    await loadSaveStates();
    console.log("🗑️ 存档删除成功");
  } catch (error) {
    console.error("删除存档失败:", error);
    alert("删除存档失败，请重试");
  }
}

async function exportSaveState(saveStateId: string): Promise<void> {
  try {
    const blob = await saveStateManager.exportSaveState(saveStateId);
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${props.game.name}_save_${new Date()
      .toISOString()
      .slice(0, 10)}.json`;
    a.click();
    URL.revokeObjectURL(url);
    console.log("📤 导出存档成功");
  } catch (error) {
    console.error("导出存档失败:", error);
  }
}

async function importSaveState(file: File): Promise<void> {
  try {
    const saveState = await saveStateManager.importSaveState(file);
    await loadSaveStates();
    console.log("📥 导入存档成功:", saveState.description);
  } catch (error) {
    console.error("导入存档失败:", error);
  }
}

function handleFileImport(event: Event): void {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    importSaveState(file);
    target.value = ""; // 清空文件输入
  }
}

// 从文件系统加载 BIOS 文件到 IndexedDB
async function loadBiosFromFileSystem(filename: string): Promise<boolean> {
  try {
    console.log(`🔍 尝试从文件系统加载 BIOS: ${filename}`);

    // 尝试从 public/games 目录加载
    const response = await fetch(`/games/${filename}`);
    if (response.ok) {
      const arrayBuffer = await response.arrayBuffer();
      const biosFile = {
        name: filename,
        data: arrayBuffer,
        size: arrayBuffer.byteLength,
        system: filename.includes("neogeo") ? "neogeo" : "arcade",
        uploadDate: new Date(),
      };

      await storeBiosFile(biosFile);
      console.log(`✅ BIOS 文件 ${filename} 从文件系统加载成功`);
      return true;
    }
  } catch (error) {
    console.log(`❌ 从文件系统加载 BIOS ${filename} 失败:`, error);
  }
  return false;
}

// BIOS 加载函数
async function loadBiosForSystem(systemType: string, gameUrl: string) {
  try {
    console.log(`🔧 检查 ${systemType} 系统的 BIOS 文件...`);

    // 对于 FBNeo 核心，确保设置不解压模式
    if (
      props.core?.id === "fbneo" ||
      props.core?.id?.includes("fbalpha") ||
      systemType === "arcade"
    ) {
      (window as any).EJS_dontExtractBIOS = false;
      console.log("🎮 FBNeo/街机模式：设置不解压 BIOS 文件");
    }

    // 从游戏文件名提取游戏名称
    const gameName = gameUrl.split("/").pop()?.split(".")[0] || "";
    console.log(`🎮 游戏名称: ${gameName}`);
    console.log(`🔍 游戏URL: ${gameUrl}`);
    console.log(`🔍 URL分割结果:`, gameUrl.split("/"));
    console.log(`🔍 文件名:`, gameUrl.split("/").pop());

    // 获取推荐的预置 BIOS - 优先使用核心 ID，然后使用游戏名称
    let recommendedBios: PresetBiosItem[] = [];

    // 首先尝试基于核心 ID 获取推荐 BIOS
    if (props.core?.id) {
      recommendedBios = getRecommendedPresetBiosByCore(props.core.id);
      console.log(
        `💡 基于核心 ${props.core.id} 推荐的 BIOS:`,
        recommendedBios.map((b) => b.name)
      );
    }

    // 如果基于核心没有找到，再尝试基于游戏名称
    if (recommendedBios.length === 0) {
      recommendedBios = getRecommendedPresetBios(gameName);
      console.log(
        `💡 基于游戏名称 ${gameName} 推荐的 BIOS:`,
        recommendedBios.map((b) => b.name)
      );
    }

    console.log(`💡 最终推荐的 BIOS 数量: ${recommendedBios.length}`);

    // 尝试加载推荐的 BIOS 文件
    for (const bios of recommendedBios) {
      console.log(`🔧 尝试加载 BIOS 文件: ${bios.filename}`);

      console.log(`🔍 查找 BIOS 文件: ${bios.filename}`);
      const biosFile = await getBiosFile(bios.filename);
      console.log(`🔍 BIOS 文件查找结果:`, biosFile ? "找到" : "未找到");
      if (biosFile) {
        const biosUrl = await getBiosFileUrl(bios.filename);
        if (biosUrl) {
          // 对于 FBNeo 核心，需要特殊处理
          if (
            props.core?.id === "fbneo" ||
            props.core?.id?.includes("fbalpha")
          ) {
            (window as any).EJS_dontExtractBIOS = false;
            (window as any).EJS_biosUrl = biosUrl;
            console.log(
              `✅ FBNeo BIOS 文件 ${bios.filename} 已加载 (不解压模式)`
            );
            console.log(
              `🔧 EJS_dontExtractBIOS = ${(window as any).EJS_dontExtractBIOS}`
            );
            console.log(`🔧 EJS_biosUrl = ${(window as any).EJS_biosUrl}`);
          } else {
            (window as any).EJS_biosUrl = biosUrl;
            console.log(`✅ BIOS 文件 ${bios.filename} 已加载`);
          }
          return; // 找到并加载了 BIOS，退出
        }
      } else {
        console.warn(`⚠️ 未找到 BIOS 文件: ${bios.filename}`);

        // 如果是可下载的预置 BIOS，自动下载
        if (bios.url && bios.isVerified) {
          console.log(`🔄 自动下载 BIOS: ${bios.name}`);

          // 显示下载提示
          toast.info(`检测到游戏需要 ${bios.name}，正在自动下载...`, {
            duration: 3000,
          });

          try {
            // 自动下载并安装 BIOS
            await downloadAndInstallPresetBios(bios.id);

            // 下载成功后重新尝试加载
            const biosFile = await getBiosFile(bios.filename);
            if (biosFile) {
              const biosUrl = await getBiosFileUrl(bios.filename);
              if (biosUrl) {
                // 对于 FBNeo 核心，需要特殊处理
                if (
                  props.core?.id === "fbneo" ||
                  props.core?.id?.includes("fbalpha")
                ) {
                  (window as any).EJS_dontExtractBIOS = false;
                  (window as any).EJS_biosUrl = biosUrl;
                  console.log(
                    `✅ FBNeo BIOS 文件 ${bios.filename} 下载并加载成功 (不解压模式)`
                  );
                } else {
                  (window as any).EJS_biosUrl = biosUrl;
                  console.log(`✅ BIOS 文件 ${bios.filename} 下载并加载成功`);
                }

                toast.success(`${bios.name} 下载成功！游戏即将启动`, {
                  duration: 3000,
                });

                return; // 成功下载并加载，退出
              }
            }
          } catch (error) {
            console.error(`下载 BIOS 失败:`, error);
            toast.error(`下载 ${bios.name} 失败，请手动在 BIOS 管理页面下载`, {
              duration: 5000,
            });
          }
        } else if (bios.url) {
          // 如果有 URL 但未验证，提示用户手动下载
          toast.warning(`游戏需要 ${bios.name}，请在 BIOS 管理页面下载`, {
            duration: 5000,
          });
        } else {
          // 没有下载链接，提示用户手动上传
          toast.warning(`游戏需要 ${bios.name}，请在 BIOS 管理页面手动上传`, {
            duration: 5000,
          });
        }
      }
    }

    // 如果没有找到推荐的 BIOS，使用传统的检测方法
    if (recommendedBios.length === 0) {
      let biosFileName = "";

      // 街机游戏 BIOS 检测
      if (
        systemType === "arcade" ||
        systemType === "fbneo" ||
        systemType === "fbalpha"
      ) {
        const lowerGameUrl = gameUrl.toLowerCase();

        // Neo Geo 游戏检测
        if (
          lowerGameUrl.includes("kof") ||
          lowerGameUrl.includes("neogeo") ||
          lowerGameUrl.includes("metal") ||
          lowerGameUrl.includes("samurai") ||
          lowerGameUrl.includes("garou") ||
          lowerGameUrl.includes("lastblade") ||
          lowerGameUrl.includes("fatfury")
        ) {
          biosFileName = "neogeo.zip";
        }
        // PGM 游戏检测
        else if (
          lowerGameUrl.includes("pgm") ||
          lowerGameUrl.includes("knights") ||
          lowerGameUrl.includes("ddp") ||
          lowerGameUrl.includes("kov") ||
          lowerGameUrl.includes("三国")
        ) {
          biosFileName = "pgm.zip";
        }
        // Naomi 游戏检测
        else if (
          lowerGameUrl.includes("naomi") ||
          lowerGameUrl.includes("crazy") ||
          lowerGameUrl.includes("taxi") ||
          lowerGameUrl.includes("virtua")
        ) {
          biosFileName = "naomi.zip";
        }
        // Atomiswave 游戏检测
        else if (
          lowerGameUrl.includes("awbios") ||
          lowerGameUrl.includes("atomiswave")
        ) {
          biosFileName = "awbios.zip";
        }
      }
      // PlayStation 游戏检测
      else if (systemType === "psx" || systemType === "playstation") {
        biosFileName = "scph1001.bin"; // 默认使用美版 BIOS
      }
      // Dreamcast 游戏检测
      else if (systemType === "dreamcast") {
        biosFileName = "dc_boot.bin";
      }

      if (biosFileName) {
        console.log(`🔧 传统方式检测到需要 BIOS: ${biosFileName}`);

        const biosFile = await getBiosFile(biosFileName);
        if (biosFile) {
          const biosUrl = await getBiosFileUrl(biosFileName);
          if (biosUrl) {
            // 对于 FBNeo 核心，需要特殊处理
            if (
              props.core?.id === "fbneo" ||
              props.core?.id?.includes("fbalpha")
            ) {
              (window as any).EJS_dontExtractBIOS = false;
              (window as any).EJS_biosUrl = biosUrl;
              console.log(
                `✅ FBNeo BIOS 文件 ${biosFileName} 已加载 (不解压模式)`
              );
            } else {
              (window as any).EJS_biosUrl = biosUrl;
              console.log(`✅ BIOS 文件 ${biosFileName} 已加载`);
            }
          }
        } else {
          console.warn(`⚠️ 未找到 BIOS 文件: ${biosFileName}`);

          // 首先尝试从文件系统加载
          console.log(`🔄 尝试从文件系统加载 BIOS: ${biosFileName}`);
          const loadedFromFS = await loadBiosFromFileSystem(biosFileName);

          if (loadedFromFS) {
            // 从文件系统加载成功，重新尝试获取
            const biosFile = await getBiosFile(biosFileName);
            if (biosFile) {
              const biosUrl = await getBiosFileUrl(biosFileName);
              if (biosUrl) {
                // 对于 FBNeo 核心，需要特殊处理
                if (
                  props.core?.id === "fbneo" ||
                  props.core?.id?.includes("fbalpha")
                ) {
                  (window as any).EJS_dontExtractBIOS = false;
                  (window as any).EJS_biosUrl = biosUrl;
                  console.log(
                    `✅ FBNeo BIOS 文件 ${biosFileName} 从文件系统加载成功 (不解压模式)`
                  );
                } else {
                  (window as any).EJS_biosUrl = biosUrl;
                  console.log(
                    `✅ BIOS 文件 ${biosFileName} 从文件系统加载成功`
                  );
                }
                return; // 成功加载，退出
              }
            }
          }

          // 如果文件系统加载失败，尝试从预置 BIOS 中找到对应的文件并自动下载
          const allPresetBios = await import("../config/presetBios.json");
          const matchingBios = allPresetBios.default.bios.find(
            (bios: any) => bios.filename === biosFileName
          );

          if (matchingBios && matchingBios.url && matchingBios.isVerified) {
            console.log(`🔄 尝试自动下载传统检测的 BIOS: ${matchingBios.name}`);

            toast.info(`检测到游戏需要 ${matchingBios.name}，正在自动下载...`, {
              duration: 3000,
            });

            try {
              await downloadAndInstallPresetBios(matchingBios.id);

              // 下载成功后重新尝试加载
              const biosFile = await getBiosFile(biosFileName);
              if (biosFile) {
                const biosUrl = await getBiosFileUrl(biosFileName);
                if (biosUrl) {
                  // 对于 FBNeo 核心，需要特殊处理
                  if (
                    props.core?.id === "fbneo" ||
                    props.core?.id?.includes("fbalpha")
                  ) {
                    (window as any).EJS_dontExtractBIOS = false;
                    (window as any).EJS_biosUrl = biosUrl;
                    console.log(
                      `✅ FBNeo BIOS 文件 ${biosFileName} 下载并加载成功 (不解压模式)`
                    );
                  } else {
                    (window as any).EJS_biosUrl = biosUrl;
                    console.log(`✅ BIOS 文件 ${biosFileName} 下载并加载成功`);
                  }

                  toast.success(`${matchingBios.name} 下载成功！游戏即将启动`, {
                    duration: 3000,
                  });

                  return;
                }
              }
            } catch (error) {
              console.error(`下载 BIOS 失败:`, error);
              toast.error(
                `下载 ${matchingBios.name} 失败，请手动在 BIOS 管理页面下载`,
                {
                  duration: 5000,
                }
              );
            }
          } else {
            toast.warning(
              `游戏需要 ${biosFileName}，请在 BIOS 管理页面上传或下载`,
              {
                duration: 5000,
              }
            );
          }
        }
      }
    }
  } catch (error) {
    console.error("加载 BIOS 文件失败:", error);
  }
}

// 监听屏幕方向变化
function handleOrientationChange() {
  isPortrait.value = window.innerHeight > window.innerWidth;
  console.log("屏幕方向变化:", isPortrait.value ? "竖屏" : "横屏");
}

onMounted(async () => {
  // 添加屏幕方向变化监听器
  window.addEventListener("resize", handleOrientationChange);
  window.addEventListener("orientationchange", handleOrientationChange);

  // 检查游戏进度
  await checkGameProgress();
});

// 监听 props 变化，当游戏和核心都准备好时初始化
watch(
  () => [props.game, props.core],
  async ([game, core]) => {
    if (game && core) {
      console.log("游戏和核心都准备好了，开始初始化...", {
        game: game.name,
        core: core.name,
      });
      try {
        await initializeRetroArch();
      } catch (error) {
        console.error("RetroArch初始化失败:", error);
        emit("error", "RetroArch初始化失败: " + (error as Error).message);
      }
    }
  },
  { immediate: true }
);

// 监听存档菜单状态，打开时加载存档列表
watch(showSaveMenu, async (isOpen) => {
  if (isOpen) {
    await loadSaveStates();
  }
});

async function initializeRetroArch() {
  try {
    loading.value = true;

    console.log("开始加载EmulatorJS模拟器...");

    // 设置EmulatorJS配置
    const gameUrl = await createGameBlob();
    const coreType = getEmulatorJSCore(props.core?.id || "gambatte");

    // 设置当前游戏名称供系统类型判断使用
    (window as any).currentGameName = props.game.name;
    const systemType = getSystemFromCore(props.core?.id || "gambatte");

    console.log("🎮 游戏信息:");
    console.log("  - 游戏名称:", props.game.name);
    console.log("  - 游戏URL:", gameUrl);
    console.log("  - 核心类型:", coreType);
    console.log("  - 系统类型:", systemType);

    // 设置回调函数（必须在加载器之前设置）
    (window as any).EJS_ready = () => {
      console.log("🎮 EmulatorJS ready!");
      loading.value = false;
    };

    (window as any).EJS_onGameStart = async () => {
      console.log("🎮 Game started!");
      loading.value = false;

      // 启动进度保存会话
      try {
        await saveStateManager.init();
        saveStateManager.startGameSession(
          props.game.name,
          getSystemFromCore(props.core?.id || "gambatte")
        );
        console.log("🎮 进度保存会话已启动");

        // 自动读取最新的自动存档
        setTimeout(async () => {
          try {
            const progress = await saveStateManager.getGameProgress(
              props.game.name
            );
            if (progress?.autoSave) {
              console.log("🔄 自动读取最新自动存档...");
              await saveStateManager.loadSaveState(progress.autoSave.id);
              console.log("✅ 自动存档读取成功");
            }
          } catch (error) {
            console.warn("自动读取存档失败:", error);
          }
        }, 2000); // 等待2秒确保游戏完全启动
      } catch (error) {
        console.error("启动进度保存会话失败:", error);
      }
    };

    (window as any).EJS_onLoadState = () => {
      console.log("🎮 Game loaded!");
    };

    // 添加错误回调
    (window as any).EJS_onError = (error: any) => {
      console.error("❌ EmulatorJS 错误:", error);
      loading.value = false;
    };

    (window as any).EJS_onGameLoaded = () => {
      console.log("🎮 游戏文件已加载");
    };

    // 配置EmulatorJS全局变量
    (window as any).EJS_player = "#canvas";

    // 对于街机游戏，强制使用fbneo核心
    if (systemType === "arcade") {
      (window as any).EJS_core = "fbneo";
    } else {
      (window as any).EJS_core = coreType;
    }
    (window as any).EJS_system = systemType;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_gameUrl = gameUrl;

    // 对于FBNeo核心，使用标准化的游戏名称
    let gameName = props.game.name;
    console.log("🔧 原始游戏名称:", gameName);

    if (coreType === "fbneo") {
      // 移除文件扩展名，FBNeo使用ROM名称而不是文件名
      gameName = props.game.name.replace(/\.(zip|neo|mvs)$/i, "");
      console.log("🔧 移除扩展名后:", gameName);

      // 标准化 KOF 游戏名称
      const lowerName = gameName.toLowerCase();
      if (lowerName.includes("kof")) {
        console.log("🔧 检测到 KOF 游戏，进行名称标准化...");
        if (lowerName.includes("97")) {
          gameName = "kof97";
          console.log("🔧 标准化为: kof97 (包含97)");
        } else if (lowerName.includes("970")) {
          // KOF970 可能是 KOF97 的变体
          gameName = "kof97";
          console.log("🔧 标准化为: kof97 (从970)");
        } else if (lowerName.includes("971")) {
          // KOF971 可能是 KOF97 的变体
          gameName = "kof97";
          console.log("🔧 标准化为: kof97 (从971)");
        }
      }
    }

    console.log("🔧 最终游戏名称:", gameName);
    (window as any).EJS_gameName = gameName;
    (window as any).EJS_startOnLoaded = true;
    (window as any).EJS_threads = false;
    (window as any).EJS_volume = 0.8;

    // 对于 FBNeo 核心，强制使用 legacy 版本
    if (
      systemType === "arcade" ||
      systemType === "neogeo" ||
      coreType === "fbneo"
    ) {
      (window as any).EJS_forceLegacyCores = true;
      (window as any).EJS_webgl2 = false; // legacy 核心可能不需要 WebGL2
      (window as any).EJS_threads = false; // 禁用多线程
      console.log("🔧 FBNeo 配置：使用 legacy 核心，禁用 WebGL2 和多线程");
    } else {
      (window as any).EJS_forceLegacyCores = false;
    }

    // 街机核心特殊配置
    if (coreType === "fbneo") {
      // FBNeo 需要从文件系统路径加载文件
      (window as any).EJS_pathParentDirectory = "/games/";
      (window as any).EJS_gameParentUrl = "/games/neogeo.zip";
      (window as any).EJS_softLoad = 0;
      (window as any).EJS_dontExtractBIOS = false;

      // 对于 FBNeo，使用文件系统路径而不是 Blob URL
      (window as any).EJS_gameUrl = `/games/${gameName}.zip`;

      // 对于 Neo Geo 游戏，设置 BIOS 路径
      if (systemType === "neogeo") {
        (window as any).EJS_biosUrl = "/games/neogeo.zip";
        console.log("🔧 Neo Geo 模式：使用文件系统 BIOS 路径");
      } else {
        (window as any).EJS_biosUrl = "";
      }

      console.log("🔧 FBNeo 配置:", {
        gameUrl: (window as any).EJS_gameUrl,
        gameParentUrl: (window as any).EJS_gameParentUrl,
        biosUrl: (window as any).EJS_biosUrl,
        pathParentDirectory: (window as any).EJS_pathParentDirectory,
        dontExtractBIOS: (window as any).EJS_dontExtractBIOS,
        gameName: (window as any).EJS_gameName,
      });
    }

    console.log("EmulatorJS配置:", {
      player: (window as any).EJS_player,
      core: (window as any).EJS_core,
      system: (window as any).EJS_system,
      pathtodata: (window as any).EJS_pathtodata,
      gameUrl: (window as any).EJS_gameUrl,
      gameName: (window as any).EJS_gameName,
      startOnLoaded: (window as any).EJS_startOnLoaded,
    });

    // 验证游戏 URL 是否可访问
    console.log("验证游戏 URL:", gameUrl);
    fetch(gameUrl)
      .then((response) => {
        console.log(
          "游戏文件响应:",
          response.status,
          response.headers.get("content-type")
        );
        return response.arrayBuffer();
      })
      .then((buffer) => {
        console.log("游戏文件大小:", buffer.byteLength, "字节");
      })
      .catch((error) => {
        console.error("游戏文件访问失败:", error);
      });

    // 动态加载EmulatorJS加载器
    await loadEmulatorJS();

    // FBNeo 核心使用 legacy 版本
    if (coreType === "fbneo") {
      // 等待EmulatorJS初始化后确保使用 legacy 核心
      setTimeout(() => {
        if ((window as any).EJS_emulator) {
          const emulator = (window as any).EJS_emulator;
          // 确保使用 legacy 核心设置
          if (emulator.supportsWebgl2 !== undefined) {
            emulator.webgl2Enabled = false;
            emulator.supportsWebgl2 = false;
          }
          console.log(
            "使用 FBNeo Legacy 核心，WebGL2:",
            emulator.webgl2Enabled
          );
        }
      }, 100);
    }
    // 给EmulatorJS一些时间来初始化
    setTimeout(() => {
      if (loading.value) {
        console.log("EmulatorJS初始化超时，尝试手动启动...");
        loading.value = false;
      }
    }, 15000); // 增加到15秒超时
  } catch (error) {
    loading.value = false;
    throw error;
  }
}

function getEmulatorJSCore(coreId: string): string {
  // EmulatorJS核心名称映射 - 直接返回核心名称
  const coreMap: { [key: string]: string } = {
    gambatte: "gambatte",
    mgba: "mgba",
    snes9x: "snes9x",
    fceumm: "fceumm",
    genesis_plus_gx: "genesis_plus_gx",
    melonds: "melonds",
    desmume: "desmume",
    fbneo: "fbneo",
  };

  return coreMap[coreId] || "gambatte";
}

function getSystemFromCore(coreId: string): string {
  const systemMap: { [key: string]: string } = {
    gambatte: "gb",
    mgba: "gba",
    snes9x: "snes",
    fceumm: "nes",
    genesis_plus_gx: "segaMD",
    melonds: "nds",
    desmume: "nds",
    fbneo: "arcade",
  };

  // 对于 FBNeo 核心，根据游戏名称判断是否为 Neo Geo
  if (coreId === "fbneo") {
    const gameName = (window as any).currentGameName || "";
    if (
      gameName.toLowerCase().includes("kof") ||
      gameName.toLowerCase().includes("neogeo") ||
      gameName.toLowerCase().includes("metal") ||
      gameName.toLowerCase().includes("samurai")
    ) {
      return "neogeo";
    }
    return "arcade";
  }

  return systemMap[coreId] || "gb";
}

async function createGameBlob(): Promise<string> {
  try {
    console.log("原始游戏数据:", props.game.data);
    console.log("游戏数据类型:", typeof props.game.data);
    console.log("游戏数据是否为数组:", Array.isArray(props.game.data));

    // 将游戏数据转换为Blob URL
    const gameData = new Uint8Array(props.game.data);
    console.log("游戏数据大小:", gameData.length, "字节");

    // 根据文件扩展名设置正确的MIME类型
    let mimeType = "application/octet-stream";

    // if (extension === "zip") {
    //   mimeType = "application/octet-stream";
    // } else if (extension === "gb" || extension === "gbc") {
    //   mimeType = "application/x-gameboy-rom";
    // } else if (extension === "gba") {
    //   mimeType = "application/x-gba-rom";
    // }

    const blob = new Blob([gameData], { type: mimeType });
    const url = URL.createObjectURL(blob);

    console.log("创建Blob URL:", url);
    console.log("MIME类型:", mimeType);

    return url;
  } catch (error) {
    console.error("创建游戏Blob失败:", error);
    throw new Error("无法创建游戏文件");
  }
}

async function loadEmulatorJS() {
  return new Promise<void>(async (resolve, reject) => {
    // 清理之前的实例
    if ((window as any).EJS_emulator) {
      try {
        (window as any).EJS_emulator.destroy();
      } catch (e) {
        console.log("清理旧实例失败:", e);
      }
      delete (window as any).EJS_emulator;
    }

    // 保存当前配置
    const gameUrl = (window as any).EJS_gameUrl;
    const coreType = (window as any).EJS_core;
    const systemType = (window as any).EJS_system;

    // 重新设置配置
    (window as any).EJS_player = "#canvas";
    (window as any).EJS_core = coreType;
    (window as any).EJS_system = systemType;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_gameUrl = gameUrl;
    (window as any).EJS_startOnLoaded = true;
    (window as any).EJS_threads = true; // 启用多线程支持
    (window as any).EJS_volume = 0.8;
    (window as any).EJS_DEBUG_XX = false;
    (window as any).EJS_webgl2 = true;

    // 浏览器兼容性检查和优化
    const canvas = document.createElement("canvas");
    const gl = canvas.getContext("webgl2") || canvas.getContext("webgl");
    const hasWebGL = !!gl;
    const hasSharedArrayBuffer = typeof SharedArrayBuffer !== "undefined";

    console.log(`🔍 浏览器兼容性检查:
      WebGL支持: ${hasWebGL ? "✅" : "❌"}
      WebGL2支持: ${!!canvas.getContext("webgl2") ? "✅" : "❌"}
      SharedArrayBuffer支持: ${hasSharedArrayBuffer ? "✅" : "❌"}
      多线程支持: ${hasSharedArrayBuffer ? "✅" : "❌"}
    `);

    // 性能优化设置
    (window as any).EJS_frameSkip = 0; // 不跳帧
    (window as any).EJS_vsync = true; // 启用垂直同步
    (window as any).EJS_fastForward = false; // 禁用快进
    (window as any).EJS_rewind = false; // 禁用倒带功能以节省内存
    (window as any).EJS_netplay = false; // 禁用网络游戏
    (window as any).EJS_cheats = false; // 禁用作弊码

    // 根据浏览器能力调整设置
    if (!hasWebGL) {
      console.warn("⚠️ WebGL不支持，降级到软件渲染");
      (window as any).EJS_videoDriver = "software";
      (window as any).EJS_webgl2 = false;
    }

    if (!hasSharedArrayBuffer) {
      console.warn("⚠️ SharedArrayBuffer不支持，禁用多线程");
      (window as any).EJS_threads = false;
    }

    // 音频优化
    (window as any).EJS_audioLatency = 64; // 降低音频延迟
    (window as any).EJS_audioDriver = "webaudio"; // 使用 WebAudio API

    // 视频优化
    (window as any).EJS_videoDriver = "gl"; // 使用 OpenGL 渲染
    (window as any).EJS_smoothing = false; // 禁用平滑以提高性能

    // NDS 特殊优化设置
    if (systemType === "nds") {
      (window as any).EJS_threads = hasSharedArrayBuffer; // 根据浏览器支持决定是否启用多线程
      (window as any).EJS_frameSkip = 0; // 先尝试不跳帧
      (window as any).EJS_audioLatency = 128; // NDS 游戏增加音频缓冲
      (window as any).EJS_threaded_video = hasSharedArrayBuffer; // 根据支持情况启用视频线程
      (window as any).EJS_gpu_screenshot = false; // 禁用GPU截图以节省性能

      // 针对新超级马里奥的特殊优化
      const gameName = props.game.name.toLowerCase();
      if (gameName.includes("马里奥") || gameName.includes("mario")) {
        (window as any).EJS_frameSkip = 1; // 马里奥游戏允许轻微跳帧
        (window as any).EJS_audioLatency = 256; // 增加音频缓冲以减少音频卡顿
        (window as any).EJS_vsync = false; // 禁用垂直同步以提高性能
        console.log("🍄 新超级马里奥专用优化已启用");
      }

      console.log("🎮 NDS 游戏性能优化已启用");
    }

    // 内存管理优化
    (window as any).EJS_memorySize = 128 * 1024 * 1024; // 设置128MB内存
    (window as any).EJS_stackSize = 5 * 1024 * 1024; // 设置5MB栈大小
    (window as any).EJS_allowMemoryGrowth = true; // 允许内存增长

    // 强制垃圾回收（定期清理内存）
    if (typeof window !== "undefined" && (window as any).gc) {
      setInterval(() => {
        try {
          (window as any).gc();
          console.log("🗑️ 执行垃圾回收");
        } catch (e) {
          // 忽略错误，某些浏览器可能不支持手动GC
        }
      }, 30000); // 每30秒执行一次
    }

    // 性能监控
    let frameCount = 0;
    let lastTime = performance.now();
    let cpuUsageHistory: number[] = [];

    const performanceMonitor = setInterval(() => {
      const currentTime = performance.now();
      const deltaTime = currentTime - lastTime;
      const fps = Math.round((frameCount * 1000) / deltaTime);

      // 估算CPU使用率（基于帧时间）
      const avgFrameTime = deltaTime / (frameCount || 1);
      const targetFrameTime = 1000 / 60; // 60 FPS 目标
      const cpuUsage = Math.min(100, (avgFrameTime / targetFrameTime) * 100);
      cpuUsageHistory.push(cpuUsage);

      // 保持最近10次的记录
      if (cpuUsageHistory.length > 10) {
        cpuUsageHistory.shift();
      }

      const avgCpuUsage =
        cpuUsageHistory.reduce((a, b) => a + b, 0) / cpuUsageHistory.length;

      if (fps < 50 || avgCpuUsage > 80) {
        console.warn(
          `⚠️ 性能警告: FPS = ${fps}, CPU使用率 = ${avgCpuUsage.toFixed(1)}%`
        );

        // 自动调整性能设置
        if (avgCpuUsage > 90) {
          console.log("🔧 自动启用性能优化...");
          (window as any).EJS_frameSkip = 2;
          (window as any).EJS_audioLatency = 512;
        }
      } else {
        console.log(
          `📊 性能正常: FPS = ${fps}, CPU使用率 = ${avgCpuUsage.toFixed(1)}%`
        );
      }

      frameCount = 0;
      lastTime = currentTime;
    }, 5000); // 每5秒检查一次

    // 清理性能监控
    onUnmounted(() => {
      if (performanceMonitor) {
        clearInterval(performanceMonitor);
      }
    });

    // // 语言和界面设置
    // (window as any).EJS_language = "en-US"; // 使用英文避免翻译问题
    // (window as any).EJS_color = "#667eea";
    // (window as any).EJS_backgroundColor = "#222";

    // // 音频设置
    // (window as any).EJS_mute = false;
    // (window as any).EJS_defaultControls = true;

    // 对于街机游戏，不要解压ZIP文件
    if (
      systemType === "arcade" ||
      coreType === "fbalpha" ||
      coreType === "fbneo"
    ) {
      (window as any).EJS_dontExtractBIOS = false;
      console.log("🎮 街机游戏模式：ZIP文件将直接传递给核心");

      // 检测和加载 BIOS 文件
      await loadBiosForSystem(systemType, gameUrl);
    }

    (window as any).EJS_onLoadState = () => {
      console.log("🎮 游戏状态加载完成");
    };

    // 添加错误处理
    (window as any).EJS_onError = (error: any) => {
      console.error("🎮 EmulatorJS 错误:", error);
    };

    // 检查是否已经加载了脚本
    const existingScript = document.querySelector(
      'script[src="/emulatorjs/loader.js"]'
    );
    if (existingScript) {
      existingScript.remove();
    }

    const script = document.createElement("script");
    script.src = "/emulatorjs/loader.js";
    script.async = true;

    script.onload = () => {
      console.log("✅ EmulatorJS加载器脚本加载成功");
      // 给脚本一些时间来初始化
      setTimeout(() => {
        if ((window as any).EJS_emulator) {
          console.log("✅ EmulatorJS初始化成功");

          // 添加事件监听器
          console.log("🎮 模拟器准备就绪");
          // 尝试自动启动游戏
          setTimeout(() => {
            try {
              console.log("尝试自动启动游戏...");
              // 查找并点击开始按钮
              const startButton = document.querySelector(".ejs_start_button");
              if (startButton) {
                console.log("找到开始按钮，自动点击...");
                (startButton as HTMLElement).click();
              } else {
                console.log("未找到开始按钮");
              }
            } catch (error) {
              console.log("自动启动游戏失败:", error);
            }
          }, 100);

          (window as any).EJS_emulator.on("start", () => {
            console.log("🎮 游戏开始");
            loading.value = false;
          });

          (window as any).EJS_emulator.on("error", (error: any) => {
            console.error("❌ EmulatorJS错误:", error);
            loading.value = false;
            emit("error", `模拟器错误: ${error}`);
          });

          resolve();
        } else {
          console.log("等待EmulatorJS初始化...");
          resolve(); // 即使没有立即初始化也继续
        }
      }, 300); // 增加到3秒等待时间
    };

    script.onerror = (error) => {
      console.error("❌ EmulatorJS加载器脚本加载失败:", error);
      reject(new Error("无法加载EmulatorJS加载器"));
    };

    document.head.appendChild(script);
  });
}

async function startGame() {
  try {
    if ((window as any).EJS_emulator) {
      console.log("手动启动游戏...");
      // 尝试不同的启动方法
      if ((window as any).EJS_emulator.startGame) {
        (window as any).EJS_emulator.startGame();
      } else if (
        (window as any).EJS_emulator.gameManager &&
        (window as any).EJS_emulator.gameManager.start
      ) {
        (window as any).EJS_emulator.gameManager.start();
      } else {
        // 模拟点击开始按钮
        const startButton = document.querySelector(".ejs_start_button");
        if (startButton) {
          console.log("点击开始按钮...");
          (startButton as HTMLElement).click();
        } else {
          console.log("未找到开始按钮");
        }
      }
    } else {
      console.log("模拟器未准备就绪");
    }
  } catch (error) {
    console.error("启动游戏失败:", error);
  }
}

function stopEmulator() {
  try {
    console.log("🛑 开始停止模拟器...");

    // 首先暂停游戏
    if ((window as any).EJS_emulator) {
      try {
        // 尝试暂停游戏
        if ((window as any).EJS_emulator.pause) {
          (window as any).EJS_emulator.pause();
        }

        // 停止主循环
        if (
          (window as any).EJS_emulator.gameManager &&
          (window as any).EJS_emulator.gameManager.toggleMainLoop
        ) {
          (window as any).EJS_emulator.gameManager.toggleMainLoop(0);
        }

        // 停止音频上下文
        if (
          (window as any).EJS_emulator.Module &&
          (window as any).EJS_emulator.Module.AL
        ) {
          const alContext = (window as any).EJS_emulator.Module.AL.currentCtx;
          if (alContext && alContext.audioCtx) {
            console.log("🔇 停止音频上下文...");

            // 停止所有音频源
            if (alContext.sources) {
              Object.values(alContext.sources).forEach((source: any) => {
                try {
                  if (source.gain) {
                    source.gain.disconnect();
                  }
                  if (source.panner) {
                    source.panner.disconnect();
                  }
                } catch (e) {
                  console.log("停止音频源时出错:", e);
                }
              });
            }

            // 关闭音频上下文
            try {
              if (alContext.audioCtx.state !== "closed") {
                alContext.audioCtx.close();
              }
            } catch (e) {
              console.log("关闭音频上下文时出错:", e);
            }
          }
        }

        // 销毁模拟器实例
        (window as any).EJS_emulator.destroy();
      } catch (e) {
        console.log("销毁模拟器实例时出错:", e);
      }
      delete (window as any).EJS_emulator;
    }

    // 清理画布
    const canvas = document.querySelector("#canvas");
    if (canvas) {
      canvas.innerHTML = "";
    }

    // 清理所有音频元素
    const audioElements = document.querySelectorAll("audio");
    audioElements.forEach((audio) => {
      try {
        audio.pause();
        audio.currentTime = 0;
        audio.src = "";
        audio.load();
      } catch (e) {
        console.log("清理音频元素时出错:", e);
      }
    });

    console.log("✅ 模拟器已完全停止");
  } catch (error) {
    console.error("停止模拟器时出错:", error);
  }
  emit("stop");
}

onUnmounted(async () => {
  // 清理事件监听器
  window.removeEventListener("resize", handleOrientationChange);
  window.removeEventListener("orientationchange", handleOrientationChange);

  // 结束游戏会话
  await saveStateManager.endGameSession();

  stopEmulator();
});
</script>
